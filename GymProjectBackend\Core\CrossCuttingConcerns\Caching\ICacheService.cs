using System;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Hybrid Cache Service Interface
    /// L1: IMemoryCache (Hızlı erişim)
    /// L2: Redis (Persistent cache)
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Cache'den veri getir (önce Memory, sonra Redis)
        /// </summary>
        Task<T> GetAsync<T>(string key) where T : class;

        /// <summary>
        /// Cache'e veri kaydet (hem Memory hem Redis)
        /// </summary>
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class;

        /// <summary>
        /// Cache'den veri sil (hem Memory hem Redis)
        /// </summary>
        Task RemoveAsync(string key);

        /// <summary>
        /// Pattern'e göre cache temizle (sadece Redis)
        /// </summary>
        Task RemoveByPatternAsync(string pattern);

        /// <summary>
        /// Cache'de key var mı kontrol et
        /// </summary>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Cache istatistikleri
        /// </summary>
        Task<CacheStats> GetStatsAsync();
    }

    /// <summary>
    /// Cache istatistikleri
    /// </summary>
    public class CacheStats
    {
        public long MemoryHits { get; set; }
        public long RedisHits { get; set; }
        public long Misses { get; set; }
        public double HitRatio => (MemoryHits + RedisHits) / (double)(MemoryHits + RedisHits + Misses) * 100;
        public long TotalKeys { get; set; }
        public string MemoryUsage { get; set; }
    }
}
