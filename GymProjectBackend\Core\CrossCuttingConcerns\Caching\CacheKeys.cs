namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache key generator - Tutarlı key yapısı için
    /// Pattern: {prefix}_{companyId}_{entity}_{id}
    /// </summary>
    public static class CacheKeys
    {
        private const string SEPARATOR = "_";
        
        // Prefixes
        private const string COMPANY_PREFIX = "company";
        private const string USER_PREFIX = "user";
        private const string SYSTEM_PREFIX = "system";
        private const string LIST_PREFIX = "list";

        #region Member Keys
        
        /// <summary>
        /// Tekil üye cache key'i
        /// Pattern: company_{companyId}_member_{memberId}
        /// </summary>
        public static string Member(int companyId, int memberId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}member{SEPARATOR}{memberId}";

        /// <summary>
        /// Aktif üyeler listesi
        /// Pattern: company_{companyId}_list_members_active
        /// </summary>
        public static string ActiveMembers(int companyId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{LIST_PREFIX}{SEPARATOR}members{SEPARATOR}active";

        /// <summary>
        /// Üye arama sonuçları
        /// Pattern: company_{companyId}_list_members_search_{searchTerm}
        /// </summary>
        public static string MemberSearch(int companyId, string searchTerm)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{LIST_PREFIX}{SEPARATOR}members{SEPARATOR}search{SEPARATOR}{searchTerm.ToLowerInvariant()}";

        #endregion

        #region User Keys

        /// <summary>
        /// Kullanıcı profili
        /// Pattern: user_{userId}_profile
        /// </summary>
        public static string UserProfile(int userId)
            => $"{USER_PREFIX}{SEPARATOR}{userId}{SEPARATOR}profile";

        /// <summary>
        /// Kullanıcı yetkileri
        /// Pattern: user_{userId}_permissions
        /// </summary>
        public static string UserPermissions(int userId)
            => $"{USER_PREFIX}{SEPARATOR}{userId}{SEPARATOR}permissions";

        /// <summary>
        /// Kullanıcının şirket bilgisi
        /// Pattern: user_{userId}_company
        /// </summary>
        public static string UserCompany(int userId)
            => $"{USER_PREFIX}{SEPARATOR}{userId}{SEPARATOR}company";

        #endregion

        #region Membership Keys

        /// <summary>
        /// Üyelik bilgisi
        /// Pattern: company_{companyId}_membership_{membershipId}
        /// </summary>
        public static string Membership(int companyId, int membershipId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}membership{SEPARATOR}{membershipId}";

        /// <summary>
        /// Üye aktif üyelikleri
        /// Pattern: company_{companyId}_member_{memberId}_memberships_active
        /// </summary>
        public static string MemberActiveMemberships(int companyId, int memberId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}member{SEPARATOR}{memberId}{SEPARATOR}memberships{SEPARATOR}active";

        #endregion

        #region System Keys

        /// <summary>
        /// Şehirler listesi
        /// Pattern: system_cities
        /// </summary>
        public static string Cities()
            => $"{SYSTEM_PREFIX}{SEPARATOR}cities";

        /// <summary>
        /// İlçeler listesi
        /// Pattern: system_towns_{cityId}
        /// </summary>
        public static string Towns(int cityId)
            => $"{SYSTEM_PREFIX}{SEPARATOR}towns{SEPARATOR}{cityId}";

        /// <summary>
        /// Üyelik tipleri
        /// Pattern: system_membership_types
        /// </summary>
        public static string MembershipTypes()
            => $"{SYSTEM_PREFIX}{SEPARATOR}membership{SEPARATOR}types";

        #endregion

        #region Company Keys

        /// <summary>
        /// Şirket bilgisi
        /// Pattern: company_{companyId}_info
        /// </summary>
        public static string CompanyInfo(int companyId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}info";

        /// <summary>
        /// Şirket ayarları
        /// Pattern: company_{companyId}_settings
        /// </summary>
        public static string CompanySettings(int companyId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}settings";

        #endregion

        #region Pattern Helpers

        /// <summary>
        /// Şirket bazlı tüm cache'leri temizlemek için pattern
        /// Pattern: company_{companyId}_*
        /// </summary>
        public static string CompanyPattern(int companyId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}*";

        /// <summary>
        /// Kullanıcı bazlı tüm cache'leri temizlemek için pattern
        /// Pattern: user_{userId}_*
        /// </summary>
        public static string UserPattern(int userId)
            => $"{USER_PREFIX}{SEPARATOR}{userId}{SEPARATOR}*";

        /// <summary>
        /// Üye bazlı tüm cache'leri temizlemek için pattern
        /// Pattern: company_{companyId}_member_{memberId}_*
        /// </summary>
        public static string MemberPattern(int companyId, int memberId)
            => $"{COMPANY_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}member{SEPARATOR}{memberId}{SEPARATOR}*";

        #endregion
    }
}
