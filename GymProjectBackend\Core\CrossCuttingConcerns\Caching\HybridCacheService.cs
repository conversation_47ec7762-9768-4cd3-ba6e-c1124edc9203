using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Hybrid Cache Service Implementation
    /// L1 Cache: IMemoryCache (Çok hızlı, geçici)
    /// L2 Cache: Redis (Hızlı, kalıcı)
    /// </summary>
    public class HybridCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly IConnectionMultiplexer _redis;
        private readonly ILogger<HybridCacheService> _logger;

        // Cache süresi ayarları
        private readonly TimeSpan _defaultL1Expiry = TimeSpan.FromMinutes(5);  // Memory cache
        private readonly TimeSpan _defaultL2Expiry = TimeSpan.FromMinutes(30); // Redis cache

        // <PERSON>statistik<PERSON>
        private long _memoryHits = 0;
        private long _redisHits = 0;
        private long _misses = 0;

        public HybridCacheService(
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            IConnectionMultiplexer redis,
            ILogger<HybridCacheService> logger)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _redis = redis;
            _logger = logger;
        }

        public async Task<T> GetAsync<T>(string key) where T : class
        {
            try
            {
                // 1. Önce Memory Cache'e bak (L1)
                if (_memoryCache.TryGetValue(key, out T memoryValue))
                {
                    Interlocked.Increment(ref _memoryHits);
                    _logger.LogDebug("Cache HIT (Memory): {Key}", key);
                    return memoryValue;
                }

                // 2. Redis'e bak (L2)
                var redisValue = await _distributedCache.GetStringAsync(key);
                if (!string.IsNullOrEmpty(redisValue))
                {
                    Interlocked.Increment(ref _redisHits);
                    _logger.LogDebug("Cache HIT (Redis): {Key}", key);

                    var deserializedValue = JsonSerializer.Deserialize<T>(redisValue);

                    // Memory'ye de kaydet (L1'e promote et)
                    _memoryCache.Set(key, deserializedValue, _defaultL1Expiry);

                    return deserializedValue;
                }

                // 3. Cache MISS
                Interlocked.Increment(ref _misses);
                _logger.LogDebug("Cache MISS: {Key}", key);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache GET error for key: {Key}", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
        {
            if (value == null)
            {
                _logger.LogWarning("Attempted to cache null value for key: {Key}", key);
                return;
            }

            try
            {
                var cacheExpiry = expiry ?? _defaultL2Expiry;
                var jsonValue = JsonSerializer.Serialize(value);

                // Hem Memory hem Redis'e kaydet
                var tasks = new[]
                {
                    Task.Run(() => _memoryCache.Set(key, value, _defaultL1Expiry)),
                    _distributedCache.SetStringAsync(key, jsonValue, new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = cacheExpiry
                    })
                };

                await Task.WhenAll(tasks);
                _logger.LogDebug("Cache SET: {Key} (Expiry: {Expiry})", key, cacheExpiry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache SET error for key: {Key}", key);
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                // Hem Memory hem Redis'den sil
                var tasks = new[]
                {
                    Task.Run(() => _memoryCache.Remove(key)),
                    _distributedCache.RemoveAsync(key)
                };

                await Task.WhenAll(tasks);
                _logger.LogDebug("Cache REMOVE: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache REMOVE error for key: {Key}", key);
            }
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                var database = _redis.GetDatabase();
                var server = _redis.GetServer(_redis.GetEndPoints()[0]);

                // Redis'den pattern'e uyan key'leri bul ve sil
                var keys = server.Keys(pattern: pattern);
                var keyArray = keys.ToArray();

                if (keyArray.Length > 0)
                {
                    await database.KeyDeleteAsync(keyArray);
                    _logger.LogDebug("Cache REMOVE BY PATTERN: {Pattern} ({Count} keys)", pattern, keyArray.Length);
                }

                // Memory cache'den de temizle (pattern matching basit)
                // Not: IMemoryCache pattern silme desteklemiyor, manuel temizlik gerekir
                _logger.LogDebug("Memory cache pattern removal not implemented for pattern: {Pattern}", pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache REMOVE BY PATTERN error for pattern: {Pattern}", pattern);
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                // Önce memory'de var mı bak
                if (_memoryCache.TryGetValue(key, out _))
                    return true;

                // Redis'de var mı bak
                var database = _redis.GetDatabase();
                return await database.KeyExistsAsync(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache EXISTS error for key: {Key}", key);
                return false;
            }
        }

        public async Task<CacheStats> GetStatsAsync()
        {
            try
            {
                var database = _redis.GetDatabase();
                var server = _redis.GetServer(_redis.GetEndPoints()[0]);

                var info = await server.InfoAsync("memory");
                var keyCount = await database.ExecuteAsync("DBSIZE");

                return new CacheStats
                {
                    MemoryHits = _memoryHits,
                    RedisHits = _redisHits,
                    Misses = _misses,
                    TotalKeys = keyCount,
                    MemoryUsage = info.FirstOrDefault(x => x.Key == "used_memory_human")?.Value ?? "Unknown"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache stats");
                return new CacheStats
                {
                    MemoryHits = _memoryHits,
                    RedisHits = _redisHits,
                    Misses = _misses,
                    TotalKeys = 0,
                    MemoryUsage = "Unknown"
                };
            }
        }
    }
}
